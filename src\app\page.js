"use client";

import Navbar from '../components/Navbar';
import Services from '../components/Services';
import Projects from '../components/Projects';
import Process from '../components/Process';
import Image from 'next/image';
import PFP from '../../public/personal/PFP.png';
import { motion, AnimatePresence } from 'framer-motion';
import { useState, useEffect, useRef } from 'react';

const words = ["move", "speak", "engage", "connect"];

const AnimatedWords = ({ words }) => {
  const [index, setIndex] = useState(0);
  const [containerWidth, setContainerWidth] = useState(0);
  const longestWordRef = useRef(null);

  useEffect(() => {
    const interval = setInterval(() => {
      setIndex((prevIndex) => (prevIndex + 1) % words.length);
    }, 2500); // Change word every 2.5 seconds

    return () => clearInterval(interval);
  }, [words]);

  useEffect(() => {
    // Measure the width of the longest word after rendering
    const updateWidth = () => {
      if (longestWordRef.current) {
        setContainerWidth(longestWordRef.current.offsetWidth);
      }
    };
    
    // Initial measurement
    updateWidth();
    
    // Add resize listener to update width when window size changes
    window.addEventListener('resize', updateWidth);
    
    // Cleanup
    return () => window.removeEventListener('resize', updateWidth);
  }, [words]); // Recalculate if words change

  const longestWord = words.reduce((longest, current) => {
    return current.length > longest.length ? current : longest;
  }, "");

  return (
    <>
      {/* Temporarily render the longest word to measure its width */}
      <span 
        ref={longestWordRef} 
        style={{ 
          position: 'absolute', 
          visibility: 'hidden', 
          whiteSpace: 'nowrap',
          fontSize: 'inherit',
          fontFamily: 'inherit',
          fontWeight: 'inherit'
        }}
      >
        {longestWord}
      </span>
      <span className="inline-block">
        <div style={{ 
          width: containerWidth, 
          display: 'inline-block',
          textAlign: 'left'
        }}>
          <AnimatePresence mode='wait'>
            <motion.span
              key={words[index]}
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              exit={{ y: -20, opacity: 0 }}
              transition={{ duration: 0.4, ease: "easeInOut" }}
              className="inline-block"
            >
              {words[index].split("").map((char, i) => (
                <motion.span
                  key={char + i}
                  initial={{ y: 20, opacity: 0 }}
                  animate={{ y: 0, opacity: 1 }}
                  exit={{ y: -20, opacity: 0 }}
                  transition={{ duration: 0.4, delay: i * 0.03 }}
                  className="inline-block"
                >
                  {char}
                </motion.span>
              ))}
            </motion.span>
          </AnimatePresence>
        </div>
      </span>
    </>
  );
};

export default function Home() {
  const [scrollY, setScrollY] = useState(0);
  const [showScrollIndicator, setShowScrollIndicator] = useState(true);
  const [windowHeight, setWindowHeight] = useState(0);

  useEffect(() => {
    // Set initial window height
    if (typeof window !== 'undefined') {
      setWindowHeight(window.innerHeight);
    }

    const handleScroll = () => {
      const currentScrollY = window.scrollY;
      setScrollY(currentScrollY);
      
      // Hide scroll indicator when user starts scrolling
      if (currentScrollY > 10) {
        setShowScrollIndicator(false);
      } else {
        setShowScrollIndicator(true);
      }
    };

    const handleResize = () => {
      if (typeof window !== 'undefined') {
        setWindowHeight(window.innerHeight);
      }
    };

    if (typeof window !== 'undefined') {
      window.addEventListener('scroll', handleScroll, { passive: true });
      window.addEventListener('resize', handleResize);
      
      return () => {
        window.removeEventListener('scroll', handleScroll);
        window.removeEventListener('resize', handleResize);
      };
    }
  }, []);

  // Calculate opacity for home content based on scroll
  const homeOpacity = windowHeight > 0 
    ? Math.max(0, 1 - (scrollY / (windowHeight * 0.5)))
    : 1;

  return (
    <div className="bg-primary text-secondary">
      {/* Navbar - Fixed at the top with highest z-index */}
      <Navbar />

      {/* Home Section - Full Screen */}
      <div 
        className="h-screen flex flex-col px-8 relative"
        style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          zIndex: 1
        }}
      >
        {/* Main Content Container */}
        <motion.div 
          className="flex flex-col flex-grow w-full mt-[64px] pb-[100px] items-center"
          style={{
            opacity: homeOpacity,
            transform: `translateY(${-scrollY * 1}px)`
          }}
        >
          {/* Main Content - centered within this container */}
          <div className="flex flex-col items-center justify-center flex-grow">
            <Image
              src={PFP}
              alt="Personal Profile Picture"
              width={150}
              height={150}
              className="rounded-full mb-8"
            />
            <h1 className="font-heading font-extrabold text-secondary mb-4 text-center"
                style={{ 
                  fontSize: 'clamp(2rem, 5vw, 3rem)',
                  lineHeight: '1.4',
                  whiteSpace: 'nowrap'
                }}>
              I make visuals that <AnimatedWords words={words}/>
            </h1>
            <p className="text-lg text-secondary max-w-prose mb-8 text-center">
              From slick banners to dynamic animations and 3D renders, I craft designs that do the job and look damn good doing it.
            </p>
          </div>

          {/* Scroll Down Indicator */}
          <div
            className="mt-auto"
            style={{
              opacity: showScrollIndicator ? 0.75 : 0,
              transition: 'opacity 0.3s ease'
            }}
          >
            {/* Scroll Down Text */}
            <div className="mb-2 text-secondary text-sm text-center">
              Scroll down
            </div>

            {/* Scroll Down Arrow */}
            <div className="scroll-down-arrow text-secondary text-3xl z-40 text-center">
              ↓
            </div>
          </div>
        </motion.div>
      </div>

      {/* Content Sections */}
      <div
        className="relative bg-primary"
        style={{
          marginTop: '90dvh',
          zIndex: 0
        }}
      >
        {/* Services Section */}
        <Services globalScrollY={scrollY} windowHeight={windowHeight} />

        {/* Projects Section */}
        <Projects />

        {/* Process Section */}
        <Process />
      </div>
    </div>
  );
}
